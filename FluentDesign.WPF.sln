﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{827E0CD3-B72D-47B6-A68D-7590B98EB39B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FluentDesign.WPF", "src\FluentDesign.WPF\FluentDesign.WPF.csproj", "{3C6D4959-35D2-4982-808F-D71DAB3DCF58}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FluentDesign.Demo", "src\FluentDesign.Demo\FluentDesign.Demo.csproj", "{65B246FB-3F86-4762-8636-0F08C1712D86}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FluentDesign.Shared", "src\FluentDesign.Shared\FluentDesign.Shared.csproj", "{5C0119F2-C34C-47DA-9FFD-F04B33EE4FF9}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{0AB3BF05-4346-4AA6-1389-037BE0695223}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FluentDesign.WPF.Tests", "tests\FluentDesign.WPF.Tests\FluentDesign.WPF.Tests.csproj", "{10B483D8-8CCB-467C-927E-2038CAA2E4B4}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{3C6D4959-35D2-4982-808F-D71DAB3DCF58}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3C6D4959-35D2-4982-808F-D71DAB3DCF58}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3C6D4959-35D2-4982-808F-D71DAB3DCF58}.Debug|x64.ActiveCfg = Debug|Any CPU
		{3C6D4959-35D2-4982-808F-D71DAB3DCF58}.Debug|x64.Build.0 = Debug|Any CPU
		{3C6D4959-35D2-4982-808F-D71DAB3DCF58}.Debug|x86.ActiveCfg = Debug|Any CPU
		{3C6D4959-35D2-4982-808F-D71DAB3DCF58}.Debug|x86.Build.0 = Debug|Any CPU
		{3C6D4959-35D2-4982-808F-D71DAB3DCF58}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3C6D4959-35D2-4982-808F-D71DAB3DCF58}.Release|Any CPU.Build.0 = Release|Any CPU
		{3C6D4959-35D2-4982-808F-D71DAB3DCF58}.Release|x64.ActiveCfg = Release|Any CPU
		{3C6D4959-35D2-4982-808F-D71DAB3DCF58}.Release|x64.Build.0 = Release|Any CPU
		{3C6D4959-35D2-4982-808F-D71DAB3DCF58}.Release|x86.ActiveCfg = Release|Any CPU
		{3C6D4959-35D2-4982-808F-D71DAB3DCF58}.Release|x86.Build.0 = Release|Any CPU
		{65B246FB-3F86-4762-8636-0F08C1712D86}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{65B246FB-3F86-4762-8636-0F08C1712D86}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{65B246FB-3F86-4762-8636-0F08C1712D86}.Debug|x64.ActiveCfg = Debug|Any CPU
		{65B246FB-3F86-4762-8636-0F08C1712D86}.Debug|x64.Build.0 = Debug|Any CPU
		{65B246FB-3F86-4762-8636-0F08C1712D86}.Debug|x86.ActiveCfg = Debug|Any CPU
		{65B246FB-3F86-4762-8636-0F08C1712D86}.Debug|x86.Build.0 = Debug|Any CPU
		{65B246FB-3F86-4762-8636-0F08C1712D86}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{65B246FB-3F86-4762-8636-0F08C1712D86}.Release|Any CPU.Build.0 = Release|Any CPU
		{65B246FB-3F86-4762-8636-0F08C1712D86}.Release|x64.ActiveCfg = Release|Any CPU
		{65B246FB-3F86-4762-8636-0F08C1712D86}.Release|x64.Build.0 = Release|Any CPU
		{65B246FB-3F86-4762-8636-0F08C1712D86}.Release|x86.ActiveCfg = Release|Any CPU
		{65B246FB-3F86-4762-8636-0F08C1712D86}.Release|x86.Build.0 = Release|Any CPU
		{5C0119F2-C34C-47DA-9FFD-F04B33EE4FF9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5C0119F2-C34C-47DA-9FFD-F04B33EE4FF9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5C0119F2-C34C-47DA-9FFD-F04B33EE4FF9}.Debug|x64.ActiveCfg = Debug|Any CPU
		{5C0119F2-C34C-47DA-9FFD-F04B33EE4FF9}.Debug|x64.Build.0 = Debug|Any CPU
		{5C0119F2-C34C-47DA-9FFD-F04B33EE4FF9}.Debug|x86.ActiveCfg = Debug|Any CPU
		{5C0119F2-C34C-47DA-9FFD-F04B33EE4FF9}.Debug|x86.Build.0 = Debug|Any CPU
		{5C0119F2-C34C-47DA-9FFD-F04B33EE4FF9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5C0119F2-C34C-47DA-9FFD-F04B33EE4FF9}.Release|Any CPU.Build.0 = Release|Any CPU
		{5C0119F2-C34C-47DA-9FFD-F04B33EE4FF9}.Release|x64.ActiveCfg = Release|Any CPU
		{5C0119F2-C34C-47DA-9FFD-F04B33EE4FF9}.Release|x64.Build.0 = Release|Any CPU
		{5C0119F2-C34C-47DA-9FFD-F04B33EE4FF9}.Release|x86.ActiveCfg = Release|Any CPU
		{5C0119F2-C34C-47DA-9FFD-F04B33EE4FF9}.Release|x86.Build.0 = Release|Any CPU
		{10B483D8-8CCB-467C-927E-2038CAA2E4B4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{10B483D8-8CCB-467C-927E-2038CAA2E4B4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{10B483D8-8CCB-467C-927E-2038CAA2E4B4}.Debug|x64.ActiveCfg = Debug|Any CPU
		{10B483D8-8CCB-467C-927E-2038CAA2E4B4}.Debug|x64.Build.0 = Debug|Any CPU
		{10B483D8-8CCB-467C-927E-2038CAA2E4B4}.Debug|x86.ActiveCfg = Debug|Any CPU
		{10B483D8-8CCB-467C-927E-2038CAA2E4B4}.Debug|x86.Build.0 = Debug|Any CPU
		{10B483D8-8CCB-467C-927E-2038CAA2E4B4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{10B483D8-8CCB-467C-927E-2038CAA2E4B4}.Release|Any CPU.Build.0 = Release|Any CPU
		{10B483D8-8CCB-467C-927E-2038CAA2E4B4}.Release|x64.ActiveCfg = Release|Any CPU
		{10B483D8-8CCB-467C-927E-2038CAA2E4B4}.Release|x64.Build.0 = Release|Any CPU
		{10B483D8-8CCB-467C-927E-2038CAA2E4B4}.Release|x86.ActiveCfg = Release|Any CPU
		{10B483D8-8CCB-467C-927E-2038CAA2E4B4}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{3C6D4959-35D2-4982-808F-D71DAB3DCF58} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{65B246FB-3F86-4762-8636-0F08C1712D86} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{5C0119F2-C34C-47DA-9FFD-F04B33EE4FF9} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{10B483D8-8CCB-467C-927E-2038CAA2E4B4} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
	EndGlobalSection
EndGlobal
