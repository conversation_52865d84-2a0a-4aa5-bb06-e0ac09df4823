{"version": 3, "targets": {"net8.0-windows7.0": {"Microsoft.Build.Tasks.Git/8.0.0": {"type": "package", "build": {"build/Microsoft.Build.Tasks.Git.props": {}, "build/Microsoft.Build.Tasks.Git.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.Build.Tasks.Git.props": {}, "buildMultiTargeting/Microsoft.Build.Tasks.Git.targets": {}}}, "Microsoft.SourceLink.Common/8.0.0": {"type": "package", "build": {"build/Microsoft.SourceLink.Common.props": {}, "build/Microsoft.SourceLink.Common.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.SourceLink.Common.props": {}, "buildMultiTargeting/Microsoft.SourceLink.Common.targets": {}}}, "Microsoft.SourceLink.GitHub/8.0.0": {"type": "package", "dependencies": {"Microsoft.Build.Tasks.Git": "8.0.0", "Microsoft.SourceLink.Common": "8.0.0"}, "build": {"build/Microsoft.SourceLink.GitHub.props": {}, "build/Microsoft.SourceLink.GitHub.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.SourceLink.GitHub.props": {}, "buildMultiTargeting/Microsoft.SourceLink.GitHub.targets": {}}}}}, "libraries": {"Microsoft.Build.Tasks.Git/8.0.0": {"sha512": "bZKfSIKJRXLTuSzLudMFte/8CempWjVamNUR5eHJizsy+iuOuO/k2gnh7W0dHJmYY0tBf+gUErfluCv5mySAOQ==", "type": "package", "path": "microsoft.build.tasks.git/8.0.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/Microsoft.Build.Tasks.Git.props", "build/Microsoft.Build.Tasks.Git.targets", "buildMultiTargeting/Microsoft.Build.Tasks.Git.props", "buildMultiTargeting/Microsoft.Build.Tasks.Git.targets", "microsoft.build.tasks.git.8.0.0.nupkg.sha512", "microsoft.build.tasks.git.nuspec", "tools/core/Microsoft.Build.Tasks.Git.dll", "tools/core/cs/Microsoft.Build.Tasks.Git.resources.dll", "tools/core/de/Microsoft.Build.Tasks.Git.resources.dll", "tools/core/es/Microsoft.Build.Tasks.Git.resources.dll", "tools/core/fr/Microsoft.Build.Tasks.Git.resources.dll", "tools/core/it/Microsoft.Build.Tasks.Git.resources.dll", "tools/core/ja/Microsoft.Build.Tasks.Git.resources.dll", "tools/core/ko/Microsoft.Build.Tasks.Git.resources.dll", "tools/core/pl/Microsoft.Build.Tasks.Git.resources.dll", "tools/core/pt-BR/Microsoft.Build.Tasks.Git.resources.dll", "tools/core/ru/Microsoft.Build.Tasks.Git.resources.dll", "tools/core/tr/Microsoft.Build.Tasks.Git.resources.dll", "tools/core/zh-Hans/Microsoft.Build.Tasks.Git.resources.dll", "tools/core/zh-Hant/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/Microsoft.Build.Tasks.Git.dll", "tools/net472/System.Collections.Immutable.dll", "tools/net472/cs/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/de/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/es/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/fr/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/it/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/ja/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/ko/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/pl/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/pt-BR/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/ru/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/tr/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/zh-Hans/Microsoft.Build.Tasks.Git.resources.dll", "tools/net472/zh-Hant/Microsoft.Build.Tasks.Git.resources.dll"]}, "Microsoft.SourceLink.Common/8.0.0": {"sha512": "dk9JPxTCIevS75HyEQ0E4OVAFhB2N+V9ShCXf8Q6FkUQZDkgLI12y679Nym1YqsiSysuQskT7Z+6nUf3yab6Vw==", "type": "package", "path": "microsoft.sourcelink.common/8.0.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/InitializeSourceControlInformation.targets", "build/Microsoft.SourceLink.Common.props", "build/Microsoft.SourceLink.Common.targets", "buildMultiTargeting/Microsoft.SourceLink.Common.props", "buildMultiTargeting/Microsoft.SourceLink.Common.targets", "microsoft.sourcelink.common.8.0.0.nupkg.sha512", "microsoft.sourcelink.common.nuspec", "tools/core/Microsoft.SourceLink.Common.dll", "tools/core/cs/Microsoft.SourceLink.Common.resources.dll", "tools/core/de/Microsoft.SourceLink.Common.resources.dll", "tools/core/es/Microsoft.SourceLink.Common.resources.dll", "tools/core/fr/Microsoft.SourceLink.Common.resources.dll", "tools/core/it/Microsoft.SourceLink.Common.resources.dll", "tools/core/ja/Microsoft.SourceLink.Common.resources.dll", "tools/core/ko/Microsoft.SourceLink.Common.resources.dll", "tools/core/pl/Microsoft.SourceLink.Common.resources.dll", "tools/core/pt-BR/Microsoft.SourceLink.Common.resources.dll", "tools/core/ru/Microsoft.SourceLink.Common.resources.dll", "tools/core/tr/Microsoft.SourceLink.Common.resources.dll", "tools/core/zh-Hans/Microsoft.SourceLink.Common.resources.dll", "tools/core/zh-Hant/Microsoft.SourceLink.Common.resources.dll", "tools/net472/Microsoft.SourceLink.Common.dll", "tools/net472/cs/Microsoft.SourceLink.Common.resources.dll", "tools/net472/de/Microsoft.SourceLink.Common.resources.dll", "tools/net472/es/Microsoft.SourceLink.Common.resources.dll", "tools/net472/fr/Microsoft.SourceLink.Common.resources.dll", "tools/net472/it/Microsoft.SourceLink.Common.resources.dll", "tools/net472/ja/Microsoft.SourceLink.Common.resources.dll", "tools/net472/ko/Microsoft.SourceLink.Common.resources.dll", "tools/net472/pl/Microsoft.SourceLink.Common.resources.dll", "tools/net472/pt-BR/Microsoft.SourceLink.Common.resources.dll", "tools/net472/ru/Microsoft.SourceLink.Common.resources.dll", "tools/net472/tr/Microsoft.SourceLink.Common.resources.dll", "tools/net472/zh-<PERSON>/Microsoft.SourceLink.Common.resources.dll", "tools/net472/zh-Hant/Microsoft.SourceLink.Common.resources.dll"]}, "Microsoft.SourceLink.GitHub/8.0.0": {"sha512": "G5q7OqtwIyGTkeIOAc3u2ZuV/kicQaec5EaRnc0pIeSnh9LUjj+PYQrJYBURvDt7twGl2PKA7nSN0kz1Zw5bnQ==", "type": "package", "path": "microsoft.sourcelink.github/8.0.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/Microsoft.SourceLink.GitHub.props", "build/Microsoft.SourceLink.GitHub.targets", "buildMultiTargeting/Microsoft.SourceLink.GitHub.props", "buildMultiTargeting/Microsoft.SourceLink.GitHub.targets", "microsoft.sourcelink.github.8.0.0.nupkg.sha512", "microsoft.sourcelink.github.nuspec", "tools/core/Microsoft.SourceLink.GitHub.dll", "tools/core/cs/Microsoft.SourceLink.GitHub.resources.dll", "tools/core/de/Microsoft.SourceLink.GitHub.resources.dll", "tools/core/es/Microsoft.SourceLink.GitHub.resources.dll", "tools/core/fr/Microsoft.SourceLink.GitHub.resources.dll", "tools/core/it/Microsoft.SourceLink.GitHub.resources.dll", "tools/core/ja/Microsoft.SourceLink.GitHub.resources.dll", "tools/core/ko/Microsoft.SourceLink.GitHub.resources.dll", "tools/core/pl/Microsoft.SourceLink.GitHub.resources.dll", "tools/core/pt-BR/Microsoft.SourceLink.GitHub.resources.dll", "tools/core/ru/Microsoft.SourceLink.GitHub.resources.dll", "tools/core/tr/Microsoft.SourceLink.GitHub.resources.dll", "tools/core/zh-Hans/Microsoft.SourceLink.GitHub.resources.dll", "tools/core/zh-Hant/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/Microsoft.SourceLink.GitHub.dll", "tools/net472/cs/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/de/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/es/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/fr/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/it/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/ja/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/ko/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/pl/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/pt-BR/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/ru/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/tr/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/zh-<PERSON>/Microsoft.SourceLink.GitHub.resources.dll", "tools/net472/zh-Hant/Microsoft.SourceLink.GitHub.resources.dll"]}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["Microsoft.SourceLink.GitHub >= 8.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "E:\\DevExpress 23.2\\Components\\Offline Packages": {}, "e:\\DevExpress 24.2\\Components\\Offline Packages": {}, "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}, "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages": {}}, "project": {"version": "1.0.0-preview", "restore": {"projectUniqueName": "D:\\02 FluentDesign.WPF\\src\\FluentDesign.Demo\\FluentDesign.Demo.csproj", "projectName": "FluentDesign.Demo", "projectPath": "D:\\02 FluentDesign.WPF\\src\\FluentDesign.Demo\\FluentDesign.Demo.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\02 FluentDesign.WPF\\src\\FluentDesign.Demo\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\DevExpress 23.2\\Components\\Offline Packages", "e:\\DevExpress 24.2\\Components\\Offline Packages", "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Microsoft.SourceLink.GitHub": {"suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}