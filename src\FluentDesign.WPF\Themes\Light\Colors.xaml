<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- FluentDesign 亮色主题颜色定义 -->
    
    <!-- 主色调 -->
    <Color x:Key="FluentAccentColor">#0078D4</Color>
    <Color x:Key="FluentAccentLightColor">#106EBE</Color>
    <Color x:Key="FluentAccentDarkColor">#005A9E</Color>
    
    <!-- 背景色 -->
    <Color x:Key="FluentBackgroundColor">#FFFFFF</Color>
    <Color x:Key="FluentBackgroundSecondaryColor">#F9F9F9</Color>
    <Color x:Key="FluentBackgroundTertiaryColor">#F3F3F3</Color>
    
    <!-- 前景色 -->
    <Color x:Key="FluentForegroundColor">#000000</Color>
    <Color x:Key="FluentForegroundSecondaryColor">#605E5C</Color>
    <Color x:Key="FluentForegroundTertiaryColor">#8A8886</Color>
    
    <!-- 边框色 -->
    <Color x:Key="FluentBorderColor">#E1E1E1</Color>
    <Color x:Key="FluentBorderSecondaryColor">#D1D1D1</Color>
    
    <!-- 状态色 -->
    <Color x:Key="FluentSuccessColor">#107C10</Color>
    <Color x:Key="FluentWarningColor">#FF8C00</Color>
    <Color x:Key="FluentErrorColor">#D13438</Color>
    <Color x:Key="FluentInfoColor">#0078D4</Color>
    
</ResourceDictionary>
