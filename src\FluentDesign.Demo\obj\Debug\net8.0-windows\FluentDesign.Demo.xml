<?xml version="1.0"?>
<doc>
    <assembly>
        <name>FluentDesign.Demo</name>
    </assembly>
    <members>
        <member name="T:FluentDesign.Demo.App">
            <summary>
            Interaction logic for App.xaml
            </summary>
            <summary>
            App
            </summary>
        </member>
        <member name="M:FluentDesign.Demo.App.InitializeComponent">
            <summary>
            InitializeComponent
            </summary>
        </member>
        <member name="M:FluentDesign.Demo.App.Main">
            <summary>
            Application Entry Point.
            </summary>
        </member>
        <member name="T:FluentDesign.Demo.MainWindow">
            <summary>
            Interaction logic for MainWindow.xaml
            </summary>
            <summary>
            MainWindow
            </summary>
        </member>
        <member name="M:FluentDesign.Demo.MainWindow.InitializeComponent">
            <summary>
            InitializeComponent
            </summary>
        </member>
    </members>
</doc>
