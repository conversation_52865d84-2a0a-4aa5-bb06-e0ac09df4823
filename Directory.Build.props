<Project>
  
  <PropertyGroup>
    <!-- 版本信息 -->
    <VersionPrefix>1.0.0</VersionPrefix>
    <VersionSuffix Condition="'$(Configuration)' == 'Debug'">preview</VersionSuffix>
    
    <!-- 通用属性 -->
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors />
    
    <!-- 包信息 -->
    <Authors>FluentDesign Team</Authors>
    <Company>FluentDesign</Company>
    <Product>FluentDesign WPF Controls</Product>
    <Copyright>Copyright © FluentDesign 2024</Copyright>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <PackageProjectUrl>https://github.com/FluentDesign/FluentDesign.WPF</PackageProjectUrl>
    <RepositoryUrl>https://github.com/FluentDesign/FluentDesign.WPF</RepositoryUrl>
    <RepositoryType>git</RepositoryType>
    <PackageTags>WPF;Fluent;Design;Controls;UI</PackageTags>
    
    <!-- 构建配置 -->
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);CS1591</NoWarn> <!-- 缺少XML注释警告 -->
    
    <!-- 调试符号 -->
    <DebugType>portable</DebugType>
    <DebugSymbols>true</DebugSymbols>
    
    <!-- 确定性构建 -->
    <Deterministic>true</Deterministic>
    <ContinuousIntegrationBuild Condition="'$(CI)' == 'true'">true</ContinuousIntegrationBuild>
  </PropertyGroup>

  <!-- 开发依赖项 -->
  <ItemGroup>
    <PackageReference Include="Microsoft.SourceLink.GitHub" Version="8.0.0" PrivateAssets="All"/>
  </ItemGroup>

  <!-- 条件编译符号 -->
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <DefineConstants>$(DefineConstants);DEBUG_MODE</DefineConstants>
  </PropertyGroup>
  
  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <DefineConstants>$(DefineConstants);RELEASE_MODE</DefineConstants>
    <Optimize>true</Optimize>
  </PropertyGroup>

</Project>
