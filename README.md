# FluentDesign WPF 控件库

基于 Fluent Design System 的现代化 WPF 控件库，提供符合 Microsoft Fluent Design 设计语言的美观且高性能的 WPF 控件集合。

## 🚀 特性

- **现代化设计**: 遵循 Fluent Design System 设计原则
- **高性能**: 优化的渲染性能，支持 60fps 流畅体验
- **主题支持**: 内置亮色和暗色主题，支持动态切换
- **完整控件集**: 提供从基础到复杂的完整控件库
- **易于使用**: 简单的 API 设计，完善的文档和示例

## 📦 项目结构

```
FluentDesign.WPF/
├── src/
│   ├── FluentDesign.WPF/        # 核心控件库
│   ├── FluentDesign.Demo/       # 控件展示和浏览器
│   └── FluentDesign.Shared/     # 共享代码和资源
├── tests/
│   └── FluentDesign.WPF.Tests/  # 单元测试
├── docs/                        # 文档
├── build/                       # 构建脚本
└── tools/                       # 开发工具
```

## 🛠️ 开发环境要求

- .NET 8.0 或更高版本
- Visual Studio 2022 或 Visual Studio Code
- Windows 10/11 (用于 WPF 开发)

## 🚀 快速开始

### 安装

通过 NuGet 包管理器安装：

```bash
Install-Package FluentDesign.WPF
```

或通过 .NET CLI：

```bash
dotnet add package FluentDesign.WPF
```

### 基本使用

1. 在 App.xaml 中引用主题资源：

```xml
<Application x:Class="YourApp.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FluentDesign.WPF;component/Themes/Generic.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
```

2. 在 XAML 中使用控件：

```xml
<Window x:Class="YourApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:fluent="clr-namespace:FluentDesign.WPF.Controls;assembly=FluentDesign.WPF">
    <Grid>
        <fluent:FluentButton Content="Hello Fluent!" 
                            Style="{StaticResource AccentButtonStyle}"/>
    </Grid>
</Window>
```

## 🎨 控件浏览器

运行 FluentDesign.Demo 项目来浏览所有可用的控件和它们的使用示例：

```bash
cd src/FluentDesign.Demo
dotnet run
```

## 📚 文档

- [API 文档](docs/api/)
- [开发指南](docs/guides/)
- [控件使用教程](docs/tutorials/)

## 🤝 贡献

欢迎贡献代码！请查看 [贡献指南](CONTRIBUTING.md) 了解如何参与项目开发。

## 📄 许可证

本项目采用 MIT 许可证。详情请查看 [LICENSE](LICENSE) 文件。

## 🙏 致谢

- Microsoft Fluent Design System
- WPF 社区
- 所有贡献者

## 📞 联系我们

- 问题反馈: [GitHub Issues](https://github.com/FluentDesign/FluentDesign.WPF/issues)
- 讨论交流: [GitHub Discussions](https://github.com/FluentDesign/FluentDesign.WPF/discussions)

---

⭐ 如果这个项目对你有帮助，请给我们一个 Star！
