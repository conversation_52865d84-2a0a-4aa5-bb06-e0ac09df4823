﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\FluentDesign.Shared\FluentDesign.Shared.csproj" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>FluentDesign.WPF</PackageId>
    <Title>FluentDesign WPF Controls</Title>
    <Description>A modern WPF control library based on Fluent Design System</Description>
    <Authors>FluentDesign Team</Authors>
    <Company>FluentDesign</Company>
    <Product>FluentDesign WPF Controls</Product>
    <Copyright>Copyright © FluentDesign 2024</Copyright>
    <PackageTags>WPF;Fluent;Design;Controls;UI</PackageTags>
    <RepositoryUrl>https://github.com/FluentDesign/FluentDesign.WPF</RepositoryUrl>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
  </PropertyGroup>

</Project>
