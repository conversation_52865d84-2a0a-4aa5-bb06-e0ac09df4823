{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"FluentDesign.Shared/1.0.0-preview": {"dependencies": {"Microsoft.SourceLink.GitHub": "8.0.0"}, "runtime": {"FluentDesign.Shared.dll": {}}}, "Microsoft.Build.Tasks.Git/8.0.0": {}, "Microsoft.SourceLink.Common/8.0.0": {}, "Microsoft.SourceLink.GitHub/8.0.0": {"dependencies": {"Microsoft.Build.Tasks.Git": "8.0.0", "Microsoft.SourceLink.Common": "8.0.0"}}}}, "libraries": {"FluentDesign.Shared/1.0.0-preview": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Build.Tasks.Git/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bZKfSIKJRXLTuSzLudMFte/8CempWjVamNUR5eHJizsy+iuOuO/k2gnh7W0dHJmYY0tBf+gUErfluCv5mySAOQ==", "path": "microsoft.build.tasks.git/8.0.0", "hashPath": "microsoft.build.tasks.git.8.0.0.nupkg.sha512"}, "Microsoft.SourceLink.Common/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dk9JPxTCIevS75HyEQ0E4OVAFhB2N+V9ShCXf8Q6FkUQZDkgLI12y679Nym1YqsiSysuQskT7Z+6nUf3yab6Vw==", "path": "microsoft.sourcelink.common/8.0.0", "hashPath": "microsoft.sourcelink.common.8.0.0.nupkg.sha512"}, "Microsoft.SourceLink.GitHub/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-G5q7OqtwIyGTkeIOAc3u2ZuV/kicQaec5EaRnc0pIeSnh9LUjj+PYQrJYBURvDt7twGl2PKA7nSN0kz1Zw5bnQ==", "path": "microsoft.sourcelink.github/8.0.0", "hashPath": "microsoft.sourcelink.github.8.0.0.nupkg.sha512"}}}