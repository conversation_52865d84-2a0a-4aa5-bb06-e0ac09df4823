﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>FluentDesign.WPF</id>
    <version>1.0.0</version>
    <title>FluentDesign WPF Controls</title>
    <authors>FluentDesign Team</authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <projectUrl>https://github.com/FluentDesign/FluentDesign.WPF</projectUrl>
    <description>A modern WPF control library based on Fluent Design System</description>
    <copyright>Copyright © FluentDesign 2024</copyright>
    <tags>WPF Fluent Design Controls UI</tags>
    <repository type="git" url="https://github.com/FluentDesign/FluentDesign.WPF" />
    <dependencies>
      <group targetFramework="net8.0-windows7.0">
        <dependency id="FluentDesign.Shared" version="1.0.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
    <frameworkReferences>
      <group targetFramework="net8.0-windows7.0">
        <frameworkReference name="Microsoft.WindowsDesktop.App.WPF" />
      </group>
    </frameworkReferences>
  </metadata>
  <files>
    <file src="D:\02 FluentDesign.WPF\src\FluentDesign.WPF\bin\Debug\net8.0-windows\FluentDesign.WPF.dll" target="lib\net8.0-windows7.0\FluentDesign.WPF.dll" />
    <file src="D:\02 FluentDesign.WPF\src\FluentDesign.WPF\bin\Debug\net8.0-windows\FluentDesign.WPF.xml" target="lib\net8.0-windows7.0\FluentDesign.WPF.xml" />
  </files>
</package>